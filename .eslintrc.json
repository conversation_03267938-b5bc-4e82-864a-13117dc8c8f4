{"parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "env": {"node": true, "es6": true, "jest": true}, "rules": {"no-console": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "off", "no-undef": "off"}}