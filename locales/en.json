{"app": {"title": "Xiaozhi-MCPHub Dashboard", "error": "Error", "closeButton": "Close", "noServers": "No MCP servers available", "loading": "Loading...", "logout": "Logout", "profile": "Profile", "changePassword": "Change Password", "toggleSidebar": "Toggle Sidebar", "welcomeUser": "Welcome, {{username}}", "name": "MCPHub"}, "about": {"title": "About", "versionInfo": "MCPHub Version: {{version}}", "newVersion": "New version available!", "currentVersion": "Current version", "newVersionAvailable": "New version {{version}} is available", "viewOnGitHub": "View on GitHub", "checkForUpdates": "Check for Updates", "checking": "Checking for updates...", "description": "Xiaozhi-MCPHub is an MCP server orchestration and governance platform by huangjunsen0406 that uses policy-based routing to organize, monitor, and scale multiple MCP servers, providing a stable and reliable MCP entry point for Xiaozhi."}, "profile": {"viewProfile": "View profile", "userCenter": "User Center"}, "sponsor": {"label": "Sponsor", "title": "Support the Project", "rewardAlt": "Reward QR Code", "supportMessage": "Support the development of MCPHub by buying me a coffee!", "supportButton": "Support on Ko-fi"}, "wechat": {"label": "WeChat", "title": "Connect via WeChat", "qrCodeAlt": "WeChat QR Code", "scanMessage": "Scan this QR code to connect with us on WeChat"}, "discord": {"label": "Discord", "title": "Join our Discord server", "community": "Join our growing community on Discord for support, discussions, and updates!"}, "theme": {"title": "Theme", "light": "Light", "dark": "Dark", "system": "System"}, "auth": {"login": "<PERSON><PERSON>", "loginTitle": "Login to MCPHub", "slogan": "The Unified Hub for MCP Servers", "subtitle": "Centralized management platform for Model Context Protocol servers. Organize, monitor, and scale multiple MCP servers with flexible routing strategies.", "username": "Username", "password": "Password", "loggingIn": "Logging in...", "emptyFields": "Username and password cannot be empty", "loginFailed": "<PERSON><PERSON> failed, please check your username and password", "loginError": "An error occurred during login", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordsNotMatch": "New password and confirmation do not match", "changePasswordSuccess": "Password changed successfully", "changePasswordError": "Failed to change password", "changePassword": "Change Password", "passwordChanged": "Password changed successfully", "passwordChangeError": "Failed to change password"}, "server": {"addServer": "Add Server", "add": "Add", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this server?", "deleteWarning": "Deleting server '{{name}}' will remove it and all its data. This action cannot be undone.", "status": "Status", "tools": "Tools", "prompts": "Prompts", "name": "Server Name", "url": "Server URL", "apiKey": "API Key", "save": "Save", "cancel": "Cancel", "invalidConfig": "Could not find configuration data for {{serverName}}", "addError": "Failed to add server", "editError": "Failed to edit server {{serverName}}", "deleteError": "Failed to delete server {{serverName}}", "updateError": "Failed to update server", "editTitle": "Edit Server: {{serverName}}", "type": "Server Type", "command": "Command", "arguments": "Arguments", "envVars": "Environment Variables", "headers": "HTTP Headers", "key": "key", "value": "value", "enabled": "Enabled", "enable": "Enable", "disable": "Disable", "requestOptions": "Configuration", "timeout": "Request Timeout", "timeoutDescription": "Timeout for requests to the MCP server (ms)", "maxTotalTimeout": "Maximum Total Timeout", "maxTotalTimeoutDescription": "Maximum total timeout for requests sent to the MCP server (ms) (Use with progress notifications)", "resetTimeoutOnProgress": "Reset Timeout on Progress", "resetTimeoutOnProgressDescription": "Reset timeout on progress notifications", "remove": "Remove", "toggleError": "Failed to toggle server {{serverName}}", "alreadyExists": "Server {{server<PERSON>ame}} already exists", "invalidData": "Invalid server data provided", "notFound": "Server {{serverName}} not found", "namePlaceholder": "Enter server name", "urlPlaceholder": "Enter server URL", "commandPlaceholder": "Enter command", "argumentsPlaceholder": "Enter arguments", "errorDetails": "<PERSON><PERSON><PERSON>", "viewErrorDetails": "View error details", "confirmVariables": "Confirm Variable Configuration", "variablesDetected": "Variables detected in configuration. Please confirm these variables are properly configured:", "detectedVariables": "Detected Variables", "confirmVariablesMessage": "Please ensure these variables are properly defined in your runtime environment. Continue adding server?", "confirmAndAdd": "Confirm and Add", "openapi": {"inputMode": "Input Mode", "inputModeUrl": "Specification URL", "inputModeSchema": "JSON Schema", "specUrl": "OpenAPI Specification URL", "schema": "OpenAPI JSON Schema", "schemaHelp": "Paste your complete OpenAPI JSON schema here", "security": "Security Type", "securityNone": "None", "securityApiKey": "API Key", "securityHttp": "HTTP Authentication", "securityOAuth2": "OAuth 2.0", "securityOpenIdConnect": "OpenID Connect", "apiKeyConfig": "API Key Configuration", "apiKeyName": "Header/Parameter Name", "apiKeyIn": "Location", "apiKeyValue": "API Key Value", "httpAuthConfig": "HTTP Authentication Configuration", "httpScheme": "Authentication Scheme", "httpCredentials": "Credentials", "oauth2Config": "OAuth 2.0 Configuration", "oauth2Token": "Access Token", "openIdConnectConfig": "OpenID Connect Configuration", "openIdConnectUrl": "Discovery URL", "openIdConnectToken": "ID Token"}}, "status": {"online": "Online", "offline": "Offline", "connecting": "Connecting"}, "errors": {"general": "Something went wrong", "network": "Network connection error. Please check your internet connection", "serverConnection": "Unable to connect to the server. Please check if the server is running", "serverAdd": "Failed to add server. Please check the server status", "serverUpdate": "Failed to edit server {{serverName}}. Please check the server status", "serverFetch": "Failed to retrieve server data. Please try again later", "initialStartup": "The server might be starting up. Please wait a moment as this process can take some time on first launch...", "serverInstall": "Failed to install server", "failedToFetchSettings": "Failed to fetch settings", "failedToUpdateRouteConfig": "Failed to update route configuration", "failedToUpdateSmartRoutingConfig": "Failed to update smart routing configuration"}, "common": {"processing": "Processing...", "save": "Save", "cancel": "Cancel", "refresh": "Refresh", "create": "Create", "creating": "Creating...", "update": "Update", "updating": "Updating...", "submitting": "Submitting...", "delete": "Delete", "remove": "Remove", "copy": "Copy", "copyId": "Copy ID", "copyUrl": "Copy URL", "copyJson": "Copy JSON", "copySuccess": "Copied to clipboard", "copyFailed": "Co<PERSON> failed", "close": "Close", "confirm": "Confirm", "language": "Language"}, "nav": {"dashboard": "Dashboard", "servers": "Servers", "groups": "Groups", "xiaozhi": "Xiao<PERSON>", "users": "Users", "settings": "Settings", "changePassword": "Change Password", "market": "Market", "logs": "Logs"}, "pages": {"dashboard": {"title": "Dashboard", "totalServers": "Total", "onlineServers": "Online", "offlineServers": "Offline", "connectingServers": "Connecting", "recentServers": "Recent Servers"}, "servers": {"title": "Servers Management"}, "groups": {"title": "Group Management"}, "users": {"title": "User Management"}, "settings": {"title": "Settings", "language": "Language", "account": "Account <PERSON><PERSON>", "password": "Change Password", "appearance": "Appearance", "routeConfig": "Security", "installConfig": "Installation", "smartRouting": "Smart Routing"}, "market": {"title": "Market Hub - Local Market"}, "logs": {"title": "System Logs"}}, "logs": {"filters": "Filters", "search": "Search logs...", "autoScroll": "Auto-scroll", "clearLogs": "Clear logs", "loading": "Loading logs...", "noLogs": "No logs available.", "noMatch": "No logs match the current filters.", "mainProcess": "Main Process", "childProcess": "Child Process", "main": "Main", "child": "Child"}, "groups": {"add": "Add", "addNew": "Add New Group", "edit": "Edit Group", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this group?", "deleteWarning": "Deleting group '{{name}}' will remove it and all its server associations. This action cannot be undone.", "name": "Group Name", "namePlaceholder": "Enter group name", "nameRequired": "Group name is required", "description": "Description", "descriptionPlaceholder": "Enter group description (optional)", "createError": "Failed to create group", "updateError": "Failed to update group", "deleteError": "Failed to delete group", "serverAddError": "Failed to add server to group", "serverRemoveError": "Failed to remove server from group", "addServer": "Add Server to Group", "selectServer": "Select a server to add", "servers": "Servers in Group", "remove": "Remove", "noGroups": "No groups available. Create a new group to get started.", "noServers": "No servers in this group.", "noServerOptions": "No servers available", "serverCount": "{{count}} Servers", "toolSelection": "Tool Selection", "toolsSelected": "Selected", "allTools": "All", "selectedTools": "Selected tools", "selectAll": "Select All", "selectNone": "Select None", "configureTools": "Configure <PERSON><PERSON>"}, "market": {"title": "Local Installation", "official": "Official", "by": "By", "unknown": "Unknown", "tools": "tools", "search": "Search", "searchPlaceholder": "Search for servers by name, category, or tags", "clearFilters": "Clear", "clearCategoryFilter": "", "clearTagFilter": "", "categories": "Categories", "tags": "Tags", "showTags": "Show tags", "hideTags": "Hide tags", "moreTags": "", "noServers": "No servers found matching your search", "backToList": "Back to list", "install": "Install", "installing": "Installing...", "installed": "Installed", "installServer": "Install Server: {{name}}", "installSuccess": "Server {{serverName}} installed successfully", "author": "Author", "license": "License", "repository": "Repository", "examples": "Examples", "arguments": "Arguments", "argumentName": "Name", "description": "Description", "required": "Required", "example": "Example", "viewSchema": "View schema", "fetchError": "Error fetching market servers", "serverNotFound": "Server not found", "searchError": "Error searching servers", "filterError": "Error filtering servers by category", "tagFilterError": "Error filtering servers by tag", "noInstallationMethod": "No installation method available for this server", "showing": "Showing {{from}}-{{to}} of {{total}} servers", "perPage": "Per page", "confirmVariablesMessage": "Please ensure these variables are properly defined in your runtime environment. Continue installing server?", "confirmAndInstall": "Confirm and Install"}, "tool": {"run": "Run", "running": "Running...", "runTool": "<PERSON>", "cancel": "Cancel", "noDescription": "No description available", "inputSchema": "Input Schema:", "runToolWithName": "Run Tool: {{name}}", "execution": "Tool Execution", "successful": "Successful", "failed": "Failed", "result": "Result:", "error": "Error", "errorDetails": "Error Details:", "noContent": "<PERSON>l executed successfully but returned no content.", "unknownError": "Unknown error occurred", "jsonResponse": "JSON Response:", "toolResult": "Tool result", "noParameters": "This tool does not require any parameters.", "selectOption": "Select an option", "enterValue": "Enter {{type}} value", "enabled": "Enabled", "enableSuccess": "Tool {{name}} enabled successfully", "disableSuccess": "Tool {{name}} disabled successfully", "toggleFailed": "Failed to toggle tool status", "parameters": "Tool Parameters", "formMode": "Form Mode", "jsonMode": "JSON Mode", "jsonConfiguration": "JSON Configuration", "invalidJsonFormat": "Invalid JSON format", "fixJsonBeforeSwitching": "Please fix JSON format before switching to form mode", "item": "Item {{index}}", "addItem": "Add {{key}} item", "enterKey": "Enter {{key}}"}, "prompt": {"run": "Get", "running": "Getting...", "result": "Prompt Result", "error": "Prompt Error", "execution": "Prompt Execution", "successful": "Successful", "failed": "Failed", "errorDetails": "Error Details:", "noContent": "Prompt executed successfully but returned no content.", "unknownError": "Unknown error occurred", "jsonResponse": "JSON Response:", "description": "Description", "messages": "Messages", "noDescription": "No description available", "runPromptWithName": "Get Prompt: {{name}}"}, "settings": {"enableGlobalRoute": "Enable Global Route", "enableGlobalRouteDescription": "Allow connections to /sse endpoint without specifying a group ID", "enableGroupNameRoute": "Enable Group Name Route", "enableGroupNameRouteDescription": "Allow connections to /sse endpoint using group names instead of just group IDs", "enableBearerAuth": "Enable Bearer Authentication", "enableBearerAuthDescription": "Require bearer token authentication for MCP requests", "bearerAuthKey": "Bearer Authentication Key", "bearerAuthKeyDescription": "The authentication key that will be required in the <PERSON><PERSON> token", "bearerAuthKeyPlaceholder": "Enter bearer authentication key", "skipAuth": "<PERSON><PERSON> Au<PERSON>nti<PERSON>", "skipAuthDescription": "Bypass login requirement for frontend and API access (DEFAULT OFF for security)", "pythonIndexUrl": "Python Package Repository URL", "pythonIndexUrlDescription": "Set UV_DEFAULT_INDEX environment variable for Python package installation", "pythonIndexUrlPlaceholder": "e.g. https://pypi.org/simple", "npmRegistry": "NPM Registry URL", "npmRegistryDescription": "Set npm_config_registry environment variable for NPM package installation", "npmRegistryPlaceholder": "e.g. https://registry.npmjs.org/", "baseUrl": "Base URL", "baseUrlDescription": "Base URL for MCP requests", "baseUrlPlaceholder": "e.g. http://localhost:3000", "installConfig": "Installation", "systemConfigUpdated": "System configuration updated successfully", "enableSmartRouting": "Enable Smart Routing", "enableSmartRoutingDescription": "Enable smart routing feature to search the most suitable tool based on input (using $smart group name)", "dbUrl": "PostgreSQL URL (requires pgvector support)", "dbUrlPlaceholder": "e.g. postgresql://user:password@localhost:5432/dbname", "openaiApiBaseUrl": "OpenAI API Base URL", "openaiApiBaseUrlPlaceholder": "https://api.openai.com/v1", "openaiApiKey": "OpenAI API Key", "openaiApiKeyPlaceholder": "Enter OpenAI API key", "openaiApiEmbeddingModel": "OpenAI Embedding Model", "openaiApiEmbeddingModelPlaceholder": "text-embedding-3-small", "smartRoutingConfigUpdated": "Smart routing configuration updated successfully", "smartRoutingRequiredFields": "OpenAI API Key is required to enable smart routing", "smartRoutingValidationError": "Please fill in the required fields before enabling Smart Routing: {{fields}}", "dbBuiltIn": "Database connection is built-in, no configuration needed"}, "dxt": {"upload": "Upload", "uploadTitle": "Upload DXT Extension", "dropFileHere": "Drop your .dxt file here", "orClickToSelect": "or click to select from your computer", "invalidFileType": "Please select a valid .dxt file", "noFileSelected": "Please select a .dxt file to upload", "uploading": "Uploading...", "uploadFailed": "Failed to upload DXT file", "installServer": "Install MCP Server from DXT", "extensionInfo": "Extension Information", "name": "Name", "version": "Version", "description": "Description", "author": "Author", "tools": "Tools", "serverName": "Server Name", "serverNamePlaceholder": "Enter a name for this server", "install": "Install", "installing": "Installing...", "installFailed": "Failed to install server from DXT", "serverExistsTitle": "Server Already Exists", "serverExistsConfirm": "Server '{{serverName}}' already exists. Do you want to override it with the new version?", "override": "Override"}, "users": {"add": "Add User", "addNew": "Add New User", "edit": "Edit User", "delete": "Delete User", "create": "Create User", "update": "Update User", "username": "Username", "password": "Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "adminRole": "Administrator", "admin": "Admin", "user": "User", "permissions": "Permissions", "adminPermissions": "Full system access", "userPermissions": "Limited access", "currentUser": "You", "noUsers": "No users found", "adminRequired": "Administrator access required to manage users", "usernameRequired": "Username is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters long", "passwordMismatch": "Passwords do not match", "usernamePlaceholder": "Enter username", "passwordPlaceholder": "Enter password", "newPasswordPlaceholder": "Leave empty to keep current password", "confirmPasswordPlaceholder": "Confirm new password", "createError": "Failed to create user", "updateError": "Failed to update user", "deleteError": "Failed to delete user", "statsError": "Failed to fetch user statistics", "deleteConfirmation": "Are you sure you want to delete user '{{username}}'? This action cannot be undone.", "confirmDelete": "Delete User", "deleteWarning": "Are you sure you want to delete user '{{username}}'? This action cannot be undone."}, "api": {"errors": {"readonly": "Readonly for demo environment", "invalid_credentials": "Invalid username or password", "serverNameRequired": "Server name is required", "serverConfigRequired": "Server configuration is required", "serverConfigInvalid": "Server configuration must include either a URL, OpenAPI specification URL or schema, or command with arguments", "serverTypeInvalid": "Server type must be one of: stdio, sse, streamable-http, openapi", "urlRequiredForType": "URL is required for {{type}} server type", "openapiSpecRequired": "OpenAPI specification URL or schema is required for openapi server type", "headersInvalidFormat": "Headers must be an object", "headersNotSupportedForStdio": "Headers are not supported for stdio server type", "serverNotFound": "Server not found", "failedToRemoveServer": "Server not found or failed to remove", "internalServerError": "Internal server error", "failedToGetServers": "Failed to get servers information", "failedToGetServerSettings": "Failed to get server settings", "failedToGetServerConfig": "Failed to get server configuration", "failedToSaveSettings": "Failed to save settings", "toolNameRequired": "Server name and tool name are required", "descriptionMustBeString": "Description must be a string", "groupIdRequired": "Group ID is required", "groupNameRequired": "Group name is required", "groupNotFound": "Group not found", "groupIdAndServerNameRequired": "Group ID and server name are required", "groupOrServerNotFound": "Group or server not found", "toolsMustBeAllOrArray": "Tools must be \"all\" or an array of strings", "serverNameAndToolNameRequired": "Server name and tool name are required", "usernameRequired": "Username is required", "userNotFound": "User not found", "failedToGetUsers": "Failed to get users information", "failedToGetUserInfo": "Failed to get user information", "failedToGetUserStats": "Failed to get user statistics", "marketServerNameRequired": "Server name is required", "marketServerNotFound": "Market server not found", "failedToGetMarketServers": "Failed to get market servers information", "failedToGetMarketServer": "Failed to get market server information", "failedToGetMarketCategories": "Failed to get market categories", "failedToGetMarketTags": "Failed to get market tags", "failedToSearchMarketServers": "Failed to search market servers", "failedToFilterMarketServers": "Failed to filter market servers", "failedToProcessDxtFile": "Failed to process DXT file", "failedToFetchXiaozhiConfig": "Failed to fetch Xiaozhi configuration", "failedToFetchEndpoints": "Failed to fetch endpoints", "failedToCreateEndpoint": "Failed to create endpoint", "failedToUpdateEndpoint": "Failed to update endpoint", "failedToDeleteEndpoint": "Failed to delete endpoint", "failedToReconnectEndpoint": "Failed to reconnect endpoint", "failedToUpdateXiaozhiConfig": "Failed to update Xiaozhi configuration"}, "success": {"serverCreated": "Server created successfully", "serverUpdated": "Server updated successfully", "serverRemoved": "Server removed successfully", "serverToggled": "Server status toggled successfully", "toolToggled": "Tool {{name}} {{action}} successfully", "toolDescriptionUpdated": "Tool {{name}} description updated successfully", "systemConfigUpdated": "System configuration updated successfully", "groupCreated": "Group created successfully", "groupUpdated": "Group updated successfully", "groupDeleted": "Group deleted successfully", "serverAddedToGroup": "Server added to group successfully", "serverRemovedFromGroup": "Server removed from group successfully", "serverToolsUpdated": "Server tools updated successfully", "endpointCreated": "Endpoint created successfully", "endpointUpdated": "Endpoint updated successfully", "endpointDeleted": "Endpoint deleted successfully", "endpointReconnecting": "Endpoint reconnection initiated", "xiaozhiConfigUpdated": "Xiaozhi configuration updated"}}, "xiaozhi": {"title": "Xiaozhi Endpoints", "description": "Manage your Xiaozhi WebSocket endpoints for MCP integration", "addEndpoint": "Add Endpoint", "url": "WebSocket URL", "group": "Group", "created": "Created", "lastConnected": "Last Connected", "enabled": "Enabled", "never": "Never", "status": {"service": "Service Status", "totalEndpoints": "Total Endpoints", "enabled": "Enabled", "disabled": "Disabled", "connected": "Connected", "disconnected": "Disconnected", "connecting": "Connecting", "unknown": "Unknown"}, "modal": {"createTitle": "Create New Endpoint", "editTitle": "Edit Endpoint"}, "empty": {"title": "No endpoints configured", "description": "Get started by creating your first Xiaozhi endpoint."}, "delete": {"title": "Delete Endpoint", "message": "Are you sure you want to delete this endpoint? This action cannot be undone."}, "reconnect": {"title": "Reconnect", "connecting": "重连中...", "maxAttempts": "Max Attempts", "initialDelay": "Initial Delay"}, "form": {"create": {"title": "Create New Endpoint"}, "edit": {"title": "Edit Endpoint"}, "name": "Name", "namePlaceholder": "Enter endpoint name", "url": "WebSocket URL", "description": "Description", "descriptionPlaceholder": "Optional description for this endpoint", "group": "Group", "noGroup": "No Group (All Tools)", "enabled": "Enable this endpoint", "reconnectSettings": "Reconnection Settings", "maxAttempts": "Max Attempts", "initialDelay": "Initial Delay (ms)", "maxDelay": "<PERSON> (ms)", "backoffMultiplier": "Backoff Multiplier", "errors": {"nameRequired": "Name is required", "urlRequired": "WebSocket URL is required", "urlInvalid": "WebSocket URL must start with ws:// or wss://", "maxAttemptsRange": "Max attempts must be between 1 and 100", "initialDelayRange": "Initial delay must be between 100ms and 60000ms", "maxDelayGreater": "Max delay must be greater than initial delay", "backoffRange": "Backoff multiplier must be between 1 and 10"}}}}