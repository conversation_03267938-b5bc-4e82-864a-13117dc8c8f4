{"app": {"title": "Xiaozhi-MCPHub 控制面板", "error": "错误", "closeButton": "关闭", "noServers": "没有可用的 MCP 服务器", "loading": "加载中...", "logout": "退出登录", "profile": "个人资料", "changePassword": "修改密码", "toggleSidebar": "切换侧边栏", "welcomeUser": "欢迎, {{username}}", "name": "MCPHub"}, "about": {"title": "关于", "versionInfo": "MCPHub 版本: {{version}}", "newVersion": "有新版本可用！", "currentVersion": "当前版本", "newVersionAvailable": "新版本 {{version}} 已发布", "viewOnGitHub": "在 GitHub 上查看", "checkForUpdates": "检查更新", "checking": "检查更新中...", "description": "Xiaozhi-MCPHub 是由 huangjunsen0406 二次开发的 MCP 服务器治理平台，通过灵活路由统一组织、监控并扩展多台 MCP 服务器，为「小智」提供稳定可靠的 MCP 服务入口。"}, "profile": {"viewProfile": "查看个人中心", "userCenter": "个人中心"}, "sponsor": {"label": "赞助", "title": "支持项目", "rewardAlt": "赞赏码", "supportMessage": "通过捐赠支持 MCPHub 的开发！", "supportButton": "在 Ko-fi 上支持"}, "wechat": {"label": "微信", "title": "微信联系", "qrCodeAlt": "微信二维码", "scanMessage": "扫描二维码添加微信"}, "discord": {"label": "Discord", "title": "加入我们的 Discord 服务器", "community": "加入我们不断壮大的 Discord 社区，获取支持、参与讨论并了解最新动态！"}, "theme": {"title": "主题", "light": "浅色", "dark": "深色", "system": "系统"}, "auth": {"login": "登录", "loginTitle": "登录 MCPHub", "slogan": "统一的 MCP 服务器管理平台", "subtitle": "模型上下文协议服务器的集中管理平台，通过灵活的路由策略组织、监控和扩展多个 MCP 服务器。", "username": "用户名", "password": "密码", "loggingIn": "登录中...", "emptyFields": "用户名和密码不能为空", "loginFailed": "登录失败，请检查用户名和密码", "loginError": "登录过程中出现错误", "currentPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认密码", "passwordsNotMatch": "新密码与确认密码不一致", "changePasswordSuccess": "密码修改成功", "changePasswordError": "修改密码失败", "changePassword": "修改密码", "passwordChanged": "密码修改成功", "passwordChangeError": "修改密码失败"}, "server": {"addServer": "添加服务器", "add": "添加", "edit": "编辑", "delete": "删除", "confirmDelete": "您确定要删除此服务器吗？", "deleteWarning": "删除服务器 '{{name}}' 将会移除该服务器及其所有数据。此操作无法撤销。", "status": "状态", "tools": "工具", "prompts": "提示词", "name": "服务器名称", "url": "服务器 URL", "apiKey": "API 密钥", "save": "保存", "cancel": "取消", "addError": "添加服务器失败", "editError": "编辑服务器 {{serverName}} 失败", "invalidConfig": "无法找到 {{serverName}} 的配置数据", "deleteError": "删除服务器 {{serverName}} 失败", "updateError": "更新服务器失败", "editTitle": "编辑服务器: {{serverName}}", "type": "服务器类型", "command": "命令", "arguments": "参数", "envVars": "环境变量", "headers": "HTTP 请求头", "key": "键", "value": "值", "enabled": "已启用", "enable": "启用", "disable": "禁用", "requestOptions": "配置", "timeout": "请求超时", "timeoutDescription": "请求超时时间（毫秒）", "maxTotalTimeout": "最大总超时", "maxTotalTimeoutDescription": "无论是否有进度通知的最大总超时时间（毫秒）", "resetTimeoutOnProgress": "收到进度通知时重置超时", "resetTimeoutOnProgressDescription": "适用于发送周期性进度更新的长时间运行操作", "remove": "移除", "toggleError": "切换服务器 {{serverName}} 状态失败", "alreadyExists": "服务器 {{serverName}} 已经存在", "invalidData": "提供的服务器数据无效", "notFound": "找不到服务器 {{serverName}}", "namePlaceholder": "请输入服务器名称", "urlPlaceholder": "请输入服务器URL", "commandPlaceholder": "请输入命令", "argumentsPlaceholder": "请输入参数", "errorDetails": "错误详情", "viewErrorDetails": "查看错误详情", "confirmVariables": "确认变量配置", "variablesDetected": "检测到配置中包含变量，请确认这些变量是否已正确配置：", "detectedVariables": "检测到的变量", "confirmVariablesMessage": "请确保这些变量在运行环境中已正确定义。是否继续添加服务器？", "confirmAndAdd": "确认并添加", "openapi": {"inputMode": "输入模式", "inputModeUrl": "规范 URL", "inputModeSchema": "JSON 模式", "specUrl": "OpenAPI 规范 URL", "schema": "OpenAPI JSON 模式", "schemaHelp": "请在此处粘贴完整的 OpenAPI JSON 模式", "security": "安全类型", "securityNone": "无", "securityApiKey": "API 密钥", "securityHttp": "HTTP 认证", "securityOAuth2": "OAuth 2.0", "securityOpenIdConnect": "OpenID Connect", "apiKeyConfig": "API 密钥配置", "apiKeyName": "请求头/参数名称", "apiKeyIn": "位置", "apiKeyValue": "API 密钥值", "httpAuthConfig": "HTTP 认证配置", "httpScheme": "认证方案", "httpCredentials": "凭据", "oauth2Config": "OAuth 2.0 配置", "oauth2Token": "访问令牌", "openIdConnectConfig": "OpenID Connect 配置", "openIdConnectUrl": "发现 URL", "openIdConnectToken": "ID 令牌"}}, "status": {"online": "在线", "offline": "离线", "connecting": "连接中"}, "errors": {"general": "发生错误", "network": "网络连接错误，请检查您的互联网连接", "serverConnection": "无法连接到服务器，请检查服务器是否正在运行", "serverAdd": "添加服务器失败，请检查服务器状态", "serverUpdate": "编辑服务器 {{serverName}} 失败，请检查服务器状态", "serverFetch": "获取服务器数据失败，请稍后重试", "initialStartup": "服务器可能正在启动中。首次启动可能需要一些时间，请耐心等候...", "serverInstall": "安装服务器失败", "failedToFetchSettings": "获取设置失败", "failedToUpdateSystemConfig": "更新系统配置失败", "failedToUpdateRouteConfig": "更新路由配置失败", "failedToUpdateSmartRoutingConfig": "更新智能路由配置失败"}, "common": {"processing": "处理中...", "save": "保存", "cancel": "取消", "refresh": "刷新", "create": "创建", "creating": "创建中...", "update": "更新", "updating": "更新中...", "submitting": "提交中...", "delete": "删除", "remove": "移除", "copy": "复制", "copyId": "复制ID", "copyUrl": "复制URL", "copyJson": "复制JSON", "copySuccess": "已复制到剪贴板", "copyFailed": "复制失败", "close": "关闭", "confirm": "确认", "language": "语言"}, "nav": {"dashboard": "仪表盘", "servers": "服务器", "settings": "设置", "changePassword": "修改密码", "groups": "分组", "xiaozhi": "小智", "users": "用户", "market": "市场", "logs": "日志"}, "pages": {"dashboard": {"title": "仪表盘", "totalServers": "总数", "onlineServers": "在线", "offlineServers": "离线", "connectingServers": "连接中", "recentServers": "最近的服务器"}, "servers": {"title": "服务器管理"}, "settings": {"title": "设置", "language": "语言", "account": "账户设置", "password": "修改密码", "appearance": "外观", "routeConfig": "安全配置", "installConfig": "安装", "smartRouting": "智能路由", "dbBuiltIn": "数据库内置"}, "groups": {"title": "分组管理"}, "users": {"title": "用户管理"}, "market": {"title": "市场中心 - 本地市场"}, "logs": {"title": "系统日志"}}, "logs": {"filters": "筛选", "search": "搜索日志...", "autoScroll": "自动滚动", "clearLogs": "清除日志", "loading": "加载日志中...", "noLogs": "暂无日志。", "noMatch": "没有匹配当前筛选条件的日志。", "mainProcess": "主进程", "childProcess": "子进程", "main": "主", "child": "子"}, "groups": {"add": "添加", "addNew": "添加新分组", "edit": "编辑分组", "delete": "删除", "confirmDelete": "您确定要删除此分组吗？", "deleteWarning": "删除分组 '{{name}}' 将会移除该分组及其所有服务器关联。此操作无法撤销。", "name": "分组名称", "namePlaceholder": "请输入分组名称", "nameRequired": "分组名称不能为空", "description": "描述", "descriptionPlaceholder": "请输入分组描述（可选）", "createError": "创建分组失败", "updateError": "更新分组失败", "deleteError": "删除分组失败", "serverAddError": "向分组添加服务器失败", "serverRemoveError": "从分组移除服务器失败", "addServer": "添加服务器到分组", "selectServer": "选择要添加的服务器", "servers": "分组中的服务器", "remove": "移除", "noGroups": "暂无可用分组。创建一个新分组以开始使用。", "noServers": "此分组中没有服务器。", "noServerOptions": "没有可用的服务器", "serverCount": "{{count}} 台服务器", "toolSelection": "工具选择", "toolsSelected": "选择", "allTools": "全部", "selectedTools": "选中的工具", "selectAll": "全选", "selectNone": "全不选", "configureTools": "配置工具"}, "market": {"title": "本地安装", "official": "官方", "by": "作者", "unknown": "未知", "tools": "工具", "search": "搜索", "searchPlaceholder": "搜索服务器名称、分类或标签", "clearFilters": "清除", "clearCategoryFilter": "", "clearTagFilter": "", "categories": "分类", "tags": "标签", "showTags": "显示标签", "hideTags": "隐藏标签", "moreTags": "", "noServers": "未找到匹配的服务器", "backToList": "返回列表", "install": "安装", "installing": "安装中...", "installed": "已安装", "installServer": "安装服务器: {{name}}", "installSuccess": "服务器 {{serverName}} 安装成功", "author": "作者", "license": "许可证", "repository": "代码仓库", "examples": "示例", "arguments": "参数", "argumentName": "名称", "description": "描述", "required": "必填", "example": "示例", "viewSchema": "查看结构", "fetchError": "获取本地市场服务器数据失败", "serverNotFound": "未找到服务器", "searchError": "搜索服务器失败", "filterError": "按分类筛选服务器失败", "tagFilterError": "按标签筛选服务器失败", "noInstallationMethod": "该服务器没有可用的安装方法", "showing": "显示 {{from}}-{{to}}/{{total}} 个服务器", "perPage": "每页显示", "confirmVariablesMessage": "请确保这些变量在运行环境中已正确定义。是否继续安装服务器？", "confirmAndInstall": "确认并安装"}, "tool": {"run": "运行", "running": "运行中...", "runTool": "运行", "cancel": "取消", "noDescription": "无描述信息", "inputSchema": "输入模式：", "runToolWithName": "运行工具：{{name}}", "execution": "工具执行", "successful": "成功", "failed": "失败", "result": "结果：", "error": "错误", "errorDetails": "错误详情：", "noContent": "工具执行成功但未返回内容。", "unknownError": "发生未知错误", "jsonResponse": "JSON 响应：", "toolResult": "工具结果", "noParameters": "此工具不需要任何参数。", "selectOption": "选择一个选项", "enterValue": "输入{{type}}值", "enabled": "已启用", "enableSuccess": "工具 {{name}} 启用成功", "disableSuccess": "工具 {{name}} 禁用成功", "toggleFailed": "切换工具状态失败", "parameters": "工具参数", "formMode": "表单模式", "jsonMode": "JSON 模式", "jsonConfiguration": "JSON 配置", "invalidJsonFormat": "无效的 JSON 格式", "fixJsonBeforeSwitching": "请修复 JSON 格式后再切换到表单模式", "item": "项目 {{index}}", "addItem": "添加 {{key}} 项目", "enterKey": "输入 {{key}}"}, "prompt": {"run": "获取", "running": "获取中...", "result": "提示词结果", "error": "提示词错误", "execution": "提示词执行", "successful": "成功", "failed": "失败", "errorDetails": "错误详情：", "noContent": "提示词执行成功但未返回内容。", "unknownError": "发生未知错误", "jsonResponse": "JSON 响应：", "description": "描述", "messages": "消息", "noDescription": "无描述信息", "runPromptWithName": "获取提示词: {{name}}"}, "settings": {"enableGlobalRoute": "启用全局路由", "enableGlobalRouteDescription": "允许不指定组 ID 就连接到 /sse 端点", "enableGroupNameRoute": "启用组名路由", "enableGroupNameRouteDescription": "允许使用组名而不仅仅是组 ID 连接到 /sse 端点", "enableBearerAuth": "启用 Bearer 认证", "enableBearerAuthDescription": "对 MCP 请求启用 Bearer 令牌认证", "bearerAuthKey": "Bearer 认证密钥", "bearerAuthKeyDescription": "Bearer 令牌中需要携带的认证密钥", "bearerAuthKeyPlaceholder": "请输入 Bearer 认证密钥", "skipAuth": "免登录开关", "skipAuthDescription": "跳过前端和 API 访问的登录要求（默认关闭确保安全性）", "pythonIndexUrl": "Python 包仓库地址", "pythonIndexUrlDescription": "设置 UV_DEFAULT_INDEX 环境变量，用于 Python 包安装", "pythonIndexUrlPlaceholder": "例如: https://mirrors.aliyun.com/pypi/simple", "npmRegistry": "NPM 仓库地址", "npmRegistryDescription": "设置 npm_config_registry 环境变量，用于 NPM 包安装", "npmRegistryPlaceholder": "例如: https://registry.npmmirror.com/", "baseUrl": "基础地址", "baseUrlDescription": "用于 MCP 请求的基础地址", "baseUrlPlaceholder": "例如: http://localhost:3000", "installConfig": "安装配置", "systemConfigUpdated": "系统配置更新成功", "enableSmartRouting": "启用智能路由", "enableSmartRoutingDescription": "开启智能路由功能，根据输入自动搜索最合适的工具（使用 $smart 分组）", "dbUrl": "PostgreSQL 连接地址（必须支持 pgvector）", "dbUrlPlaceholder": "例如: postgresql://user:password@localhost:5432/dbname", "openaiApiBaseUrl": "OpenAI API 基础地址", "openaiApiBaseUrlPlaceholder": "https://api.openai.com/v1", "openaiApiKey": "OpenAI API 密钥", "openaiApiKeyDescription": "用于访问 OpenAI API 的密钥", "openaiApiKeyPlaceholder": "请输入 OpenAI API 密钥", "openaiApiEmbeddingModel": "OpenAI 嵌入模型", "openaiApiEmbeddingModelPlaceholder": "text-embedding-3-small", "smartRoutingConfigUpdated": "智能路由配置更新成功", "smartRoutingRequiredFields": "启用智能路由需要填写 OpenAI API 密钥", "smartRoutingValidationError": "启用智能路由前请先填写必要字段：{{fields}}", "dbBuiltIn": "数据库连接已内置，无需配置"}, "dxt": {"upload": "上传", "uploadTitle": "上传 DXT 扩展", "dropFileHere": "将 .dxt 文件拖拽到此处", "orClickToSelect": "或点击从计算机选择", "invalidFileType": "请选择有效的 .dxt 文件", "noFileSelected": "请选择要上传的 .dxt 文件", "uploading": "上传中...", "uploadFailed": "上传 DXT 文件失败", "installServer": "从 DXT 安装 MCP 服务器", "extensionInfo": "扩展信息", "name": "名称", "version": "版本", "description": "描述", "author": "作者", "tools": "工具", "serverName": "服务器名称", "serverNamePlaceholder": "为此服务器输入名称", "install": "安装", "installing": "安装中...", "installFailed": "从 DXT 安装服务器失败", "serverExistsTitle": "服务器已存在", "serverExistsConfirm": "服务器 '{{serverName}}' 已存在。是否要用新版本覆盖它？", "override": "覆盖"}, "users": {"add": "添加", "addNew": "添加新用户", "edit": "编辑用户", "delete": "删除用户", "create": "创建", "update": "更新", "username": "用户名", "password": "密码", "newPassword": "新密码", "confirmPassword": "确认密码", "adminRole": "管理员", "admin": "管理员", "user": "用户", "permissions": "权限", "adminPermissions": "完全系统访问权限", "userPermissions": "受限访问权限", "currentUser": "当前用户", "noUsers": "没有找到用户", "adminRequired": "需要管理员权限才能管理用户", "usernameRequired": "用户名是必需的", "passwordRequired": "密码是必需的", "passwordTooShort": "密码至少需要6个字符", "passwordMismatch": "密码不匹配", "usernamePlaceholder": "输入用户名", "passwordPlaceholder": "输入密码", "newPasswordPlaceholder": "留空保持当前密码", "confirmPasswordPlaceholder": "确认新密码", "createError": "创建用户失败", "updateError": "更新用户失败", "deleteError": "删除用户失败", "statsError": "获取用户统计失败", "deleteConfirmation": "您确定要删除用户 '{{username}}' 吗？此操作无法撤消。", "confirmDelete": "删除用户", "deleteWarning": "您确定要删除用户 '{{username}}' 吗？此操作无法撤消。"}, "api": {"errors": {"readonly": "演示环境无法修改数据", "invalid_credentials": "用户名或密码错误", "serverNameRequired": "服务器名称是必需的", "serverConfigRequired": "服务器配置是必需的", "serverConfigInvalid": "服务器配置必须包含 URL、OpenAPI 规范 URL 或模式，或者带参数的命令", "serverTypeInvalid": "服务器类型必须是以下之一：stdio、sse、streamable-http、openapi", "urlRequiredForType": "{{type}} 服务器类型需要 URL", "openapiSpecRequired": "openapi 服务器类型需要 OpenAPI 规范 URL 或模式", "headersInvalidFormat": "请求头必须是对象格式", "headersNotSupportedForStdio": "stdio 服务器类型不支持请求头", "serverNotFound": "找不到服务器", "failedToRemoveServer": "找不到服务器或删除失败", "internalServerError": "服务器内部错误", "failedToGetServers": "获取服务器信息失败", "failedToGetServerSettings": "获取服务器设置失败", "failedToGetServerConfig": "获取服务器配置失败", "failedToSaveSettings": "保存设置失败", "toolNameRequired": "服务器名称和工具名称是必需的", "descriptionMustBeString": "描述必须是字符串", "groupIdRequired": "分组 ID 是必需的", "groupNameRequired": "分组名称是必需的", "groupNotFound": "找不到分组", "groupIdAndServerNameRequired": "分组 ID 和服务器名称是必需的", "groupOrServerNotFound": "找不到分组或服务器", "toolsMustBeAllOrArray": "工具必须是 \"all\" 或字符串数组", "serverNameAndToolNameRequired": "服务器名称和工具名称是必需的", "usernameRequired": "用户名是必需的", "userNotFound": "找不到用户", "failedToGetUsers": "获取用户信息失败", "failedToGetUserInfo": "获取用户信息失败", "failedToGetUserStats": "获取用户统计信息失败", "marketServerNameRequired": "服务器名称是必需的", "marketServerNotFound": "找不到市场服务器", "failedToGetMarketServers": "获取市场服务器信息失败", "failedToGetMarketServer": "获取市场服务器信息失败", "failedToGetMarketCategories": "获取市场类别失败", "failedToGetMarketTags": "获取市场标签失败", "failedToSearchMarketServers": "搜索市场服务器失败", "failedToFilterMarketServers": "过滤市场服务器失败", "failedToProcessDxtFile": "处理 DXT 文件失败", "failedToFetchXiaozhiConfig": "获取小智配置失败", "failedToFetchEndpoints": "获取端点失败", "failedToCreateEndpoint": "创建端点失败", "failedToUpdateEndpoint": "更新端点失败", "failedToDeleteEndpoint": "删除端点失败", "failedToReconnectEndpoint": "重连端点失败", "failedToUpdateXiaozhiConfig": "更新小智配置失败"}, "success": {"serverCreated": "服务器创建成功", "serverUpdated": "服务器更新成功", "serverRemoved": "服务器删除成功", "serverToggled": "服务器状态切换成功", "toolToggled": "工具 {{name}} {{action}} 成功", "toolDescriptionUpdated": "工具 {{name}} 描述更新成功", "systemConfigUpdated": "系统配置更新成功", "groupCreated": "分组创建成功", "groupUpdated": "分组更新成功", "groupDeleted": "分组删除成功", "serverAddedToGroup": "服务器添加到分组成功", "serverRemovedFromGroup": "服务器从分组移除成功", "serverToolsUpdated": "服务器工具更新成功", "endpointCreated": "端点创建成功", "endpointUpdated": "端点更新成功", "endpointDeleted": "端点删除成功", "endpointReconnecting": "端点重连已启动", "xiaozhiConfigUpdated": "小智配置更新成功"}}, "xiaozhi": {"title": "小智端点", "description": "管理您的小智 WebSocket 端点以实现 MCP 集成", "addEndpoint": "添加端点", "url": "WebSocket URL", "group": "分组", "created": "创建时间", "lastConnected": "最后连接", "enabled": "已启用", "never": "从未", "status": {"service": "服务状态", "totalEndpoints": "总端点数", "enabled": "已启用", "disabled": "已禁用", "connected": "已连接", "disconnected": "已断开", "connecting": "连接中", "unknown": "未知"}, "empty": {"title": "未配置端点", "description": "通过创建您的第一个小智端点来开始使用。"}, "delete": {"title": "删除端点", "message": "您确定要删除此端点吗？此操作无法撤销。"}, "reconnect": {"title": "重连", "connecting": "重连中...", "maxAttempts": "最大尝试次数", "initialDelay": "初始延迟"}, "modal": {"createTitle": "创建新端点", "editTitle": "编辑端点"}, "form": {"create": {"title": "创建新端点"}, "edit": {"title": "编辑端点"}, "name": "名称", "namePlaceholder": "输入端点名称", "url": "WebSocket URL", "description": "描述", "descriptionPlaceholder": "为此端点输入可选描述", "group": "分组", "noGroup": "无分组（所有工具）", "enabled": "启用此端点", "reconnectSettings": "重连设置", "maxAttempts": "最大尝试次数", "initialDelay": "初始延迟（毫秒）", "maxDelay": "最大延迟（毫秒）", "backoffMultiplier": "退避倍数", "errors": {"nameRequired": "名称为必填项", "urlRequired": "WebSocket URL 为必填项", "urlInvalid": "WebSocket URL 必须以 ws:// 或 wss:// 开头", "maxAttemptsRange": "最大尝试次数必须在 1 到 100 之间", "initialDelayRange": "初始延迟必须在 100 到 60000 毫秒之间", "maxDelayGreater": "最大延迟必须大于初始延迟", "backoffRange": "退避倍数必须在 1 到 10 之间"}}}}