{"name": "@huangjunsen0406/xiaozhi-mcphub", "version": "1.0.0", "description": "MCPHub enhanced with Xiaozhi AI platform integration - 为小智AI平台优化的MCP工具桥接系统", "main": "dist/index.js", "type": "module", "bin": {"xiaozhi-mcphub": "bin/cli.js"}, "files": ["dist", "bin", "servers.json", "frontend/dist", "README.md", "LICENSE", "NOTICE"], "scripts": {"build": "pnpm backend:build && pnpm frontend:build", "backend:build": "tsc", "start": "node dist/index.js", "backend:dev": "tsx watch src/index.ts", "backend:debug": "tsx watch src/index.ts --inspect", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:ci": "jest --ci --coverage --watchAll=false", "frontend:dev": "cd frontend && vite", "frontend:build": "cd frontend && vite build", "frontend:preview": "cd frontend && vite preview", "dev": "concurrently \"pnpm backend:dev\" \"pnpm frontend:dev\"", "debug": "concurrently \"pnpm backend:debug\" \"pnpm frontend:dev\"", "prepublishOnly": "npm run build && node scripts/verify-dist.js", "docs:dev": "npm run --prefix documents docs:dev", "docs:build": "npm run --prefix documents docs:build", "docs:serve": "npm run --prefix documents docs:serve"}, "keywords": ["typescript", "server", "mcp", "model context protocol", "xia<PERSON><PERSON>", "ai platform", "websocket", "tool bridge"], "author": "", "license": "ISC", "dependencies": {"@apidevtools/swagger-parser": "^11.0.1", "@modelcontextprotocol/sdk": "^1.17.4", "@types/adm-zip": "^0.5.7", "@types/multer": "^1.4.13", "@types/pg": "^8.15.5", "adm-zip": "^0.5.16", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "dotenv-expand": "^12.0.2", "express": "^4.21.2", "express-validator": "^7.2.1", "i18next-fs-backend": "^2.6.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "openai": "^4.104.0", "openapi-types": "^12.1.3", "pg": "^8.16.3", "pgvector": "^0.2.1", "postgres": "^3.4.7", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.26", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-slot": "^1.2.3", "@shadcn/ui": "^0.0.4", "@swc/core": "^1.13.5", "@swc/jest": "^0.2.39", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.1.12", "@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.17.2", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.2.0", "eslint": "^8.57.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.2.0", "jest": "^29.7.0", "jest-environment-node": "^30.0.5", "jest-mock-extended": "4.0.0-beta1", "lucide-react": "^0.486.0", "next": "^15.5.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.7.2", "react-router-dom": "^7.8.2", "supertest": "^7.1.4", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^4.1.12", "ts-jest": "^29.4.1", "ts-node-dev": "^2.0.0", "tsx": "^4.20.5", "typescript": "^5.9.2", "vite": "^6.3.5", "zod": "^3.25.76"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "packageManager": "pnpm@10.12.4+sha256.cadfd9e6c9fcc2cb76fe7c0779a5250b632898aea5f53d833a73690c77a778d9", "pnpm": {"overrides": {"brace-expansion@1.1.11": "1.1.12", "brace-expansion@2.0.1": "2.0.2"}}}