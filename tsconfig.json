{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "dist"]}