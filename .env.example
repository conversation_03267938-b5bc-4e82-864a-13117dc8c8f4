# ========================================
# 基础应用配置
# ========================================

# 服务器端口（默认：3000）
PORT=3000

# 运行环境（默认：development）
# 影响 JWT 密钥生成策略等
NODE_ENV=development

# 应用基础路径（默认：空）
# 用于反向代理等场景，如设置为 /api
# BASE_PATH=

# 只读模式（默认：false）
# 设置为 true 禁止所有修改操作
# READONLY=false

# 初始化超时时间，单位毫秒（默认：300000，即5分钟）
# INIT_TIMEOUT=300000

# ========================================
# 数据库配置
# ========================================

# PostgreSQL 数据库连接字符串（必需）
DATABASE_URL=postgres://xiaozhi:xiaozhi123456@localhost:5434/xiaozhi_mcphub

# ========================================
# 智能路由配置（可选）
# ========================================

# 启用智能路由功能（默认：false）
# 注意：智能路由现在使用内置的 PostgreSQL 数据库存储向量数据
# 无需单独配置数据库连接
# SMART_ROUTING_ENABLED=false

# OpenAI API 配置（智能路由启用时必需）
# OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxx
# OPENAI_API_BASE_URL=https://api.openai.com/v1
# OPENAI_API_EMBEDDING_MODEL=text-embedding-3-small

# ========================================
# JWT 认证配置
# ========================================

# JWT 密钥（production 环境必需，development 环境会自动生成）
# JWT_SECRET=your-secret-key-here

# ========================================
# 调试配置
# ========================================

# 启用调试模式（默认：false）
# DEBUG=false

# ========================================
# 小智端点配置
# ========================================

# 启用快速重连模式（默认：false）
# 当设置为 true 时，端点断开后会立即尝试重连，不使用退避延迟
# 适用于网络稳定但需要快速恢复连接的场景
# XIAOZHI_AGGRESSIVE_RECONNECT=false

# 重连间隔时间，单位毫秒（默认：2000，即2秒）
# 仅在 XIAOZHI_AGGRESSIVE_RECONNECT=true 时生效
# XIAOZHI_RECONNECT_INTERVAL=2000

# 无限重连最大次数（默认：48，即24小时）
# 超过此次数后停止重连，设置为 0 表示永不停止
# XIAOZHI_MAX_INFINITE_RETRIES=48

# 无限重连休眠阈值（默认：12，即6小时）
# 连续失败这么多次后，进入休眠模式（延长重连间隔）
# XIAOZHI_SLEEP_THRESHOLD=12

# 休眠模式重连间隔，单位毫秒（默认：7200000，即2小时）
# XIAOZHI_SLEEP_INTERVAL=7200000

# ========================================
# 其他配置
# ========================================

# MCP 设置文件路径（默认：自动检测）
# MCPHUB_SETTING_PATH=/custom/path/to/settings
