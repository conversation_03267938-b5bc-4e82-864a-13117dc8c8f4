name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linter
        run: pnpm lint

      - name: Run type checking
        run: pnpm backend:build

      - name: Run tests
        run: pnpm test:ci

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # build:
  #   runs-on: ubuntu-latest
  #   needs: test

  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4

  #     - name: Setup Node.js
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: '20.x'

  #     - name: Enable Corepack
  #       run: corepack enable

  #     - name: Install dependencies
  #       run: pnpm install --frozen-lockfile

  #     - name: Build application
  #       run: pnpm build

  #     - name: Verify build artifacts
  #       run: node scripts/verify-dist.js

  # integration-test:
  #   runs-on: ubuntu-latest
  #   needs: test

  #   services:
  #     postgres:
  #       image: postgres:15
  #       env:
  #         POSTGRES_PASSWORD: postgres
  #         POSTGRES_DB: mcphub_test
  #       options: >-
  #         --health-cmd pg_isready
  #         --health-interval 10s
  #         --health-timeout 5s
  #         --health-retries 5

  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4

  #     - name: Setup Node.js
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: '20.x'

  #     - name: Enable Corepack
  #       run: corepack enable

  #     - name: Install dependencies
  #       run: pnpm install --frozen-lockfile

  #     - name: Build application
  #       run: pnpm build

  #     - name: Run integration tests
  #       run: |
  #         export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/mcphub_test"
  #         node test-integration.ts
  #       env:
  #         NODE_ENV: test
