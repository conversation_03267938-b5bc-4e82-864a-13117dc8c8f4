name: Build

on:
  push:
    tags: ['v*.*.*']
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # 打 tag 时把 package.json 的 version 同步为 tag 中的版本号
      - name: Update version from tag
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          set -euo pipefail
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "Updating package.json version to $VERSION"
          jq ".version = \"$VERSION\"" package.json > package.json.tmp
          mv package.json.tmp package.json
          echo "Updated version in package.json:"
          grep -m 1 '"version"' package.json

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: huangjunsen/xiaozhi-mcphub
          tags: |
            # 手动触发 -> edge
            type=raw,value=edge,enable=${{ github.event_name == 'workflow_dispatch' }}
            # 打 tag v1.2.3 -> 1.2.3 与 latest
            type=semver,pattern={{version}},enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') }}
          flavor: |
            latest=false

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64
          build-args: |
            INSTALL_EXT=true
          # 如果 Dockerfile 存在 'AS runtime' 阶段，则可取消注释下一行
          # target: runtime

      - name: Verify pushed images
        if: ${{ github.event_name != 'pull_request' }}
        run: |
          echo "Pushed tags:"
          echo "${{ steps.meta.outputs.tags }}"
